
import React from 'react';
import type { AspectRatio } from '../types';

interface ImageSkeletonProps {
    aspectRatio: AspectRatio;
}

const getAspectRatioClass = (ratio: AspectRatio): string => {
    switch (ratio) {
        case '16:9': return 'aspect-[16/9]';
        case '9:16': return 'aspect-[9/16]';
        case '4:3': return 'aspect-[4/3]';
        case '3:4': return 'aspect-[3/4]';
        case '1:1':
        default:
            return 'aspect-square';
    }
}

const ImageSkeleton: React.FC<ImageSkeletonProps> = ({ aspectRatio }) => {
    const aspectRatioClass = getAspectRatioClass(aspectRatio);

    return (
        <div className={`relative w-full bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 rounded-2xl overflow-hidden backdrop-blur-sm ${aspectRatioClass}`}>
            {/* Animated Background */}
            <div className="absolute inset-0 bg-gradient-to-r from-slate-800/50 via-slate-700/50 to-slate-800/50 animate-pulse"></div>

            {/* Shimmer Effect */}
            <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>

            {/* Content Placeholder */}
            <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                    {/* AI Icon */}
                    <div className="w-12 h-12 mx-auto bg-slate-700/50 rounded-xl flex items-center justify-center animate-pulse">
                        <svg className="w-6 h-6 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                        </svg>
                    </div>

                    {/* Loading Dots */}
                    <div className="flex items-center justify-center gap-1">
                        <div className="w-2 h-2 bg-indigo-500/60 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                        <div className="w-2 h-2 bg-purple-500/60 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                        <div className="w-2 h-2 bg-pink-500/60 rounded-full animate-bounce"></div>
                    </div>

                    {/* Loading Text */}
                    <div className="text-xs text-slate-400 font-medium">
                        Creating...
                    </div>
                </div>
            </div>

            {/* Border Glow Animation */}
            <div className="absolute inset-0 rounded-2xl border border-transparent bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 animate-pulse"></div>
        </div>
    );
};

export default ImageSkeleton;