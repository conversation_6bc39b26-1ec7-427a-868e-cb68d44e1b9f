
import React, { useState, useCallback } from 'react';
import { generateImages } from '../services/geminiService';
import type { AspectRatio } from '../types';
import { IMAGE_MODELS, ASPECT_RATIOS, IMAGE_COUNTS } from '../constants';
import ControlPanel from './ControlPanel';
import ImageCard from './ImageCard';
import ImageSkeleton from './ImageSkeleton';

const ImageGenerator: React.FC = () => {
    const [prompt, setPrompt] = useState<string>('A photorealistic image of a majestic lion in the savanna at sunset, with a dramatic sky.');
    const [numberOfImages, setNumberOfImages] = useState<number>(1);
    const [aspectRatio, setAspectRatio] = useState<AspectRatio>('16:9');
    const [model] = useState<string>(IMAGE_MODELS[0]);

    const [generatedImages, setGeneratedImages] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const handleGenerate = useCallback(async () => {
        if (!prompt.trim()) {
            setError("Prompt cannot be empty.");
            return;
        }
        setIsLoading(true);
        setError(null);
        setGeneratedImages([]);

        try {
            const imageBytesArray = await generateImages({
                prompt,
                model,
                numberOfImages,
                aspectRatio,
            });
            const base64Images = imageBytesArray.map(bytes => `data:image/jpeg;base64,${bytes}`);
            setGeneratedImages(base64Images);
        } catch (err) {
            setError(err instanceof Error ? err.message : "An unexpected error occurred.");
            setGeneratedImages([]);
        } finally {
            setIsLoading(false);
        }
    }, [prompt, model, numberOfImages, aspectRatio]);

    return (
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-8 min-h-[calc(100vh-200px)]">
            {/* Left Column - Input Controls */}
            <div className="xl:col-span-4 space-y-6">
                <div className="sticky top-8">
                    <div className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-3 h-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-pulse"></div>
                            <h2 className="text-xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                                Create Your Vision
                            </h2>
                        </div>

                        <ControlPanel
                            prompt={prompt}
                            setPrompt={setPrompt}
                            numberOfImages={numberOfImages}
                            setNumberOfImages={setNumberOfImages}
                            aspectRatio={aspectRatio}
                            setAspectRatio={setAspectRatio}
                            imageModels={IMAGE_MODELS}
                            selectedModel={model}
                            aspectRatios={ASPECT_RATIOS}
                            imageCounts={IMAGE_COUNTS}
                            onGenerate={handleGenerate}
                            isLoading={isLoading}
                        />
                    </div>

                    {/* Generation Stats */}
                    <div className="mt-6 bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-xl p-4">
                        <div className="grid grid-cols-2 gap-4 text-center">
                            <div className="space-y-1">
                                <div className="text-2xl font-bold text-indigo-400">{generatedImages.length}</div>
                                <div className="text-xs text-slate-400 uppercase tracking-wide">Generated</div>
                            </div>
                            <div className="space-y-1">
                                <div className="text-2xl font-bold text-purple-400">{numberOfImages}</div>
                                <div className="text-xs text-slate-400 uppercase tracking-wide">Requested</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Right Column - Output Showcase */}
            <div className="xl:col-span-8">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full animate-pulse"></div>
                            <h2 className="text-xl font-bold bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent">
                                Generated Gallery
                            </h2>
                        </div>
                        {generatedImages.length > 0 && (
                            <div className="text-sm text-slate-400">
                                {generatedImages.length} image{generatedImages.length !== 1 ? 's' : ''}
                            </div>
                        )}
                    </div>

                    {/* Error Display */}
                    {error && (
                        <div className="bg-red-900/20 border border-red-700/50 text-red-300 px-6 py-4 rounded-xl backdrop-blur-sm animate-in slide-in-from-top-2 duration-300">
                            <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                <p><span className="font-semibold">Error:</span> {error}</p>
                            </div>
                        </div>
                    )}

                    {/* Loading State */}
                    {isLoading && (
                        <div className="space-y-8 animate-in fade-in duration-500">
                            {/* Enhanced Loading Header */}
                            <div className="text-center space-y-6">
                                {/* Animated AI Brain Icon */}
                                <div className="relative mx-auto w-20 h-20">
                                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full animate-spin opacity-20"></div>
                                    <div className="absolute inset-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-full animate-pulse"></div>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <svg className="w-10 h-10 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                                        </svg>
                                    </div>
                                </div>

                                {/* Progress Dots */}
                                <div className="flex items-center justify-center gap-3">
                                    <div className="w-3 h-3 bg-indigo-500 rounded-full animate-bounce [animation-delay:-0.4s]"></div>
                                    <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce [animation-delay:-0.2s]"></div>
                                    <div className="w-3 h-3 bg-pink-500 rounded-full animate-bounce"></div>
                                    <div className="w-3 h-3 bg-emerald-500 rounded-full animate-bounce [animation-delay:0.2s]"></div>
                                </div>

                                {/* Progress Bar */}
                                <div className="max-w-xs mx-auto">
                                    <div className="h-2 bg-slate-800 rounded-full overflow-hidden">
                                        <div className="h-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>

                            {/* Image Skeletons Grid */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {Array.from({ length: numberOfImages }).map((_, index) => (
                                    <div
                                        key={index}
                                        className="animate-in fade-in duration-700 hover:scale-105 transition-transform"
                                        style={{ animationDelay: `${index * 150}ms` }}
                                    >
                                        <ImageSkeleton aspectRatio={aspectRatio} />
                                    </div>
                                ))}
                            </div>

                            {/* Generation Info */}
                            <div className="text-center">
                                <div className="inline-flex items-center gap-3 bg-slate-800/50 backdrop-blur-sm px-6 py-3 rounded-full border border-slate-700/50">
                                    <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                                    <span className="text-sm text-slate-300 font-medium">
                                        Generating {numberOfImages} image{numberOfImages !== 1 ? 's' : ''} • {aspectRatio}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Empty State */}
                    {!isLoading && generatedImages.length === 0 && !error && (
                        <div className="text-center py-20 animate-in fade-in duration-500">
                            <div className="max-w-md mx-auto space-y-6">
                                <div className="relative">
                                    <div className="w-24 h-24 mx-auto bg-gradient-to-br from-slate-700 to-slate-800 rounded-2xl border-2 border-dashed border-slate-600 flex items-center justify-center">
                                        <svg className="w-10 h-10 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <h3 className="text-2xl font-bold text-slate-300">Ready to Create?</h3>
                                    <p className="text-slate-400">Your AI-generated masterpieces will appear here. Describe your vision and watch it come to life!</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Generated Images */}
                    {!isLoading && generatedImages.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 animate-in fade-in duration-500">
                            {generatedImages.map((imgSrc, index) => (
                                <div
                                    key={index}
                                    className="animate-in zoom-in duration-500"
                                    style={{ animationDelay: `${index * 150}ms` }}
                                >
                                    <ImageCard src={imgSrc} prompt={prompt} />
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ImageGenerator;